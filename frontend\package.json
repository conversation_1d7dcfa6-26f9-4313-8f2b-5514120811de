{"name": "frontend", "proxy": "http://localhost:8080", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-brands-svg-icons": "^6.4.2", "@fortawesome/free-regular-svg-icons": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@mantine/core": "^6.0.20", "@tensorflow/tfjs": "^4.11.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.5.0", "bootstrap": "^5.3.2", "colors": "^1.4.0", "concurrently": "^9.2.1", "cors": "^2.8.5", "csvtojson": "^2.0.10", "dotenv": "^17.2.2", "express": "^5.1.0", "mongoose": "^8.18.1", "morgan": "^1.10.1", "multer": "^2.0.2", "nodemon": "^3.1.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "slugify": "^1.6.6", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.3.3"}}