import mongoose from "mongoose";
import csv<PERSON><PERSON><PERSON> from "csvto<PERSON><PERSON>";
import dotenv from "dotenv";
import colors from "colors";
import plantModel from "../models/plantModel.js";
import path from "path";
import { fileURLToPath } from "url";

// Config dotenv
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Database connection
const connectDB = async () => {
  try {
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI is not defined in environment variables");
    }

    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    console.log(`✅ Connected to MongoDB: ${conn.connection.host}`.bgMagenta.white);
  } catch (error) {
    console.error(`❌ MongoDB Connection Error: ${error.message}`.bgRed.white);
    process.exit(1);
  }
};

// Function to populate database with plant data
const populateDatabase = async () => {
  try {
    console.log("🌱 Starting database population...".bgBlue.white);
    
    // Connect to database
    await connectDB();
    
    // Clear existing data
    await plantModel.deleteMany({});
    console.log("🗑️  Cleared existing plant data".yellow);
    
    // Read CSV file
    const csvFilePath = path.join(__dirname, "../public/uploads/plant-features.csv");
    console.log("📁 Reading CSV file from:", csvFilePath);
    
    const jsonArray = await csvtojson().fromFile(csvFilePath);
    console.log(`📊 Found ${jsonArray.length} plants in CSV`.cyan);
    
    // Process and insert data
    let successCount = 0;
    let errorCount = 0;
    
    for (const plantData of jsonArray) {
      try {
        // Clean and validate data
        const cleanedData = {
          scientificName: plantData.scientificName?.trim(),
          localName: plantData.localName?.trim(),
          features: plantData.features?.trim(),
          photo: plantData.photo?.trim()
        };
        
        // Skip if essential data is missing
        if (!cleanedData.scientificName) {
          console.log(`⚠️  Skipping plant with missing scientific name`.yellow);
          errorCount++;
          continue;
        }
        
        // Create new plant document
        const newPlant = new plantModel(cleanedData);
        await newPlant.save();
        
        successCount++;
        
        if (successCount % 50 === 0) {
          console.log(`✅ Processed ${successCount} plants...`.green);
        }
        
      } catch (error) {
        errorCount++;
        console.log(`❌ Error processing plant ${plantData.scientificName}: ${error.message}`.red);
      }
    }
    
    console.log("\n🎉 Database population completed!".bgGreen.black);
    console.log(`✅ Successfully added: ${successCount} plants`.green);
    console.log(`❌ Errors encountered: ${errorCount} plants`.red);
    
    // Verify the data
    const totalPlants = await plantModel.countDocuments();
    console.log(`📊 Total plants in database: ${totalPlants}`.cyan);
    
    // Show some sample data
    const samplePlants = await plantModel.find().limit(5);
    console.log("\n🔍 Sample plants in database:".bgCyan.black);
    samplePlants.forEach((plant, index) => {
      console.log(`${index + 1}. ${plant.scientificName} (${plant.localName})`.white);
    });
    
  } catch (error) {
    console.error(`💥 Fatal error during population: ${error.message}`.bgRed.white);
    console.error(error.stack);
  } finally {
    // Close database connection
    await mongoose.connection.close();
    console.log("\n🔌 Database connection closed".gray);
    process.exit(0);
  }
};

// Run the population script
populateDatabase();
