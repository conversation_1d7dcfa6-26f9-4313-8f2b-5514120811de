import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import upload from "../Assets/upload_image.png";
import "../style/dragDropStyle.css";
import Footer from "./footer";
import { useNavigate } from "react-router-dom";
import * as tf from "@tensorflow/tfjs";
import { plantClasses } from "../data/plantClasses";

// Function to load an image and convert it to a tensor
async function loadImage(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => resolve(tf.browser.fromPixels(img));
      img.src = event.target.result;
    };
    reader.readAsDataURL(file);
  });
}

function preprocessImage(image) {
  const resizedImage = image.resizeBilinear([128, 128]); // Adjust size if necessary
  return resizedImage;
}

const Dragdrop = () => {
  const [files, setFiles] = useState([]);
  const [modelLoading, setModelLoading] = useState(true);
  const [model, setModel] = useState(null);
  const navigate = useNavigate();

  // Load the TensorFlow model
  const loadModel = async () => {
    try {
      const model = await tf.loadGraphModel("/model/model.json");
      setModel(model);
      setModelLoading(false);
    } catch (error) {
      console.error("Error loading model:", error);
      setModelLoading(false);
    }
  };

  React.useEffect(() => {
    loadModel();
  }, []);

  const getPlantName = async (files) => {
    try {
      const image = await loadImage(files[0]);
      let tensor = preprocessImage(image);
      tensor = tensor.expandDims(0);
      const predictions = await model.predict(tensor).data();
      let index = predictions.indexOf(Math.max(...predictions));
      navigate(`/result/${plantClasses[index]}`);
    } catch (error) {
      console.error("Error predicting plant:", error);
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [],
    },
    onDrop: (acceptedFiles) => {
      getPlantName(acceptedFiles);
    },
  });

  return (
    <>
      {!modelLoading && (
        <>
          <section className="container">
            <div {...getRootProps({ className: "dropzone" })}>
              <input {...getInputProps()} />
              <img src={upload} alt="upload" style={{ width: 60 }} />
              <h2>
                Click here to add files <br />
                or
                <br /> Drag & Drop files here
              </h2>
              <button className="btn btn-outline-info" style={{ marginTop: 0 }}>
                Browse Files
              </button>
            </div>
          </section>
          <Footer />
        </>
      )}
    </>
  );
};

export default Dragdrop;