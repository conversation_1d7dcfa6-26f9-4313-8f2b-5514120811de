import * as tf from "@tensorflow/tfjs";
import React, { useState } from "react";
import { useDropzone } from "react-dropzone";
import { useNavigate } from "react-router-dom";
import upload from "../Assets/upload_image.png";
import { plantClasses } from "../data/plantClasses";
import "../style/dragDropStyle.css";
import Footer from "./footer";

// Function to load an image and convert it to a tensor
async function loadImage(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => resolve(tf.browser.fromPixels(img));
      img.src = event.target.result;
    };
    reader.readAsDataURL(file);
  });
}

function preprocessImage(image) {
  const resizedImage = image.resizeBilinear([128, 128]); // Adjust size if necessary
  return resizedImage;
}

const Dragdrop = () => {
  const [files, setFiles] = useState([]);
  const [modelLoading, setModelLoading] = useState(true);
  const [predicting, setPredicting] = useState(false);
  const [model, setModel] = useState(null);
  const navigate = useNavigate();

  // Load the TensorFlow model
  const loadModel = async () => {
    try {
      const model = await tf.loadGraphModel("/model/model.json");
      setModel(model);
      setModelLoading(false);
    } catch (error) {
      console.error("Error loading model:", error);
      setModelLoading(false);
    }
  };

  React.useEffect(() => {
    loadModel();
  }, []);

  const getPlantName = async (files) => {
    try {
      if (!model) {
        console.error("Model not loaded yet");
        alert("Model is still loading. Please wait and try again.");
        return;
      }

      setPredicting(true);
      console.log("Starting plant prediction...");

      const image = await loadImage(files[0]);
      let tensor = preprocessImage(image);
      tensor = tensor.expandDims(0);

      console.log("Running model prediction...");
      const predictions = await model.predict(tensor).data();
      let index = predictions.indexOf(Math.max(...predictions));
      const predictedPlant = plantClasses[index];
      const confidence = Math.max(...predictions);

      console.log("Prediction results:", {
        index,
        plantName: predictedPlant,
        confidence: confidence.toFixed(4)
      });

      // Navigate to result page with the predicted plant name
      navigate(`/result/${encodeURIComponent(predictedPlant)}`);

    } catch (error) {
      console.error("Error predicting plant:", error);
      alert("Error occurred during plant prediction. Please try again.");
    } finally {
      setPredicting(false);
    }
  };

  const { getRootProps, getInputProps } = useDropzone({
    accept: {
      "image/*": [],
    },
    onDrop: (acceptedFiles) => {
      getPlantName(acceptedFiles);
    },
  });

  return (
    <>
      {modelLoading ? (
        <div className="container text-center" style={{ marginTop: "100px" }}>
          <div className="spinner-border text-primary" role="status">
            <span className="sr-only">Loading...</span>
          </div>
          <h3 style={{ marginTop: "20px" }}>Loading AI Model...</h3>
          <p>Please wait while we prepare the plant recognition model.</p>
        </div>
      ) : predicting ? (
        <div className="container text-center" style={{ marginTop: "100px" }}>
          <div className="spinner-border text-success" role="status">
            <span className="sr-only">Predicting...</span>
          </div>
          <h3 style={{ marginTop: "20px" }}>Analyzing Plant...</h3>
          <p>Our AI is identifying your plant. This may take a few seconds.</p>
        </div>
      ) : (
        <>
          <section className="container">
            <div {...getRootProps({ className: "dropzone" })}>
              <input {...getInputProps()} />
              <img src={upload} alt="upload" style={{ width: 60 }} />
              <h2>
                Click here to add files <br />
                or
                <br /> Drag & Drop files here
              </h2>
              <button className="btn btn-outline-info" style={{ marginTop: 0 }}>
                Browse Files
              </button>
            </div>
          </section>
          <Footer />
        </>
      )}
    </>
  );
};

export default Dragdrop;