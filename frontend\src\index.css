@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-image: url("./Assets/background1.jpeg");
  background-size: cover;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/*------------------------------------------------------------------------*/
/*-------------------Home Page--------------------------------------------*/
.Home {
  padding: 60px;
  display: flex;
  justify-content: center;
  align-items: center;

}

.content {
  border: 35px solid #D9D9D9;
  border-radius: 1.5%;

  background-color: #D1FFBD;
  box-shadow: -10px 5px 10px black;
  box-shadow: inset;
  width: 1000px;
  height: auto;
  padding: 20px;
}

.title h1 {
  font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
  background-image: url("https://rare-gallery.com/uploads/posts/582448-banana-leaf.jpg");
  background-size: cover;
  padding: 20px;
  font-size: 70px;
  border-radius: 1.5%;

}

.content img {
  width: 930px;
  height: 350px;

}

.para {
  font-family: Cambria, Cochin, Georgia, Times, 'Times New Roman', serif;
  font-size: larger;
  padding: 20px 20px 0px;
}

.foot {
  padding: 0px 50px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.foot img {
  margin-left: 150px;
}