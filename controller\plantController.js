import feedbackModel from "../models/feedbackModel.js";
import plantModel from "../models/plantModel.js";

// Import the plant classes from frontend
const classLabels = [
  "<PERSON><PERSON><PERSON> sagittifolius", "<PERSON>b<PERSON> precatorius", "<PERSON>tilon indicum", "Acanthus integrifolius", "Acorus <PERSON>i",
  "Agave americana", "Ageratum conyzoides", "Allium ramosum", "Alocasia macrorrhizos", "Aloe vera",
  "Alpinia officinarum", "Amomum longiligulare", "Ampelopsis cantoniensis", "Andrographis paniculata", "Angelica dahurica",
  "<PERSON><PERSON><PERSON> sylvestris", "Artemisia vulgaris", "Artocarpus altilis", "Artocarpus heterophyllus", "Artocarpus lakoocha",
  "Asparagus cochinchinensis", "Asparagus officinalis", "Averrhoa carambola", "Baccaurea sp", "<PERSON><PERSON>ia lupulina",
  "Bengal Arum", "Berchemia lineata", "Bidens pilosa", "Bischofia trifoliata", "Blackberry Lily",
  "Blumea balsamifera", "Boehmeria nivea", "Breynia vitis", "Caesalpinia sappan", "Callerya speciosa",
  "Callisia fragrans", "Calophyllum inophyllum", "Calotropis gigantea", "Camellia chrysantha", "Caprifoliaceae",
  "Capsicum annuum", "Carica papaya", "Catharanthus roseus", "Celastrus hindsii", "Celosia argentea",
  "Centella asiatica", "Citrus aurantifolia", "Citrus hystrix", "Clausena indica", "Cleistocalyx operculatus",
  "Clerodendrum inerme", "Clinacanthus nutans", "Clycyrrhiza uralensis fish", "Coix lacryma-jobi", "Cordyline fruticosa",
  "Costus speciosus", "Crescentia cujete Lin", "Crinum asiaticum", "Crinum latifolium", "Croton oblongifolius",
  "Croton tonkinensis", "Curculigo gracilis", "Curculigo orchioides", "Cymbopogon", "Datura metel",
  "Derris elliptica", "Dianella ensifolia", "Dicliptera chinensis", "Dimocarpus longan", "Dioscorea persimilis",
  "Eichhoriaceae crassipes", "Eleutherine bulbosa", "Erythrina variegata", "Eupatorium fortunei", "Eupatorium triplinerve",
  "Euphorbia hirta", "Euphorbia pulcherrima", "Euphorbia tirucalli", "Euphorbia tithymaloides", "Eurycoma longifolia",
  "Excoecaria cochinchinensis", "Excoecaria sp", "Fallopia multiflora", "Ficus auriculata", "Ficus racemosa",
  "Fructus lycii", "Glochidion eriocarpum", "Glycosmis pentaphylla", "Gonocaryum lobbianum", "Gymnema sylvestre",
  "Gynura divaricata", "Hemerocallis fulva", "Hemigraphis glaucescens", "Hibiscus mutabilis", "Hibiscus rosa sinensis",
  "Hibiscus sabdariffa", "Holarrhena pubescens", "Homalomena occulta", "Houttuynia cordata", "Imperata cylindrica",
  "Iris domestica", "Ixora coccinea", "Jasminum sambac", "Jatropha gossypiifolia", "Jatropha multifida",
  "Jatropha podagrica", "Justicia gendarussa", "Kalanchoe pinnata", "Lactuca indica", "Lantana camara",
  "Lawsonia inermis", "Leea rubra", "Litsea Glutinosa", "Lonicera dasystyla", "Lpomoea sp",
  "Maesa", "Mallotus barbatus", "Mangifera", "Melastoma malabathricum", "Mentha Spicata",
  "Microcos tomentosa", "Micromelum falcatum", "Millettia pulchra", "Mimosa pudica", "Morinda citrifolia",
  "Moringa oleifera", "Morus alba", "Mussaenda philippica", "Nelumbo nucifera", "Ocimum basilicum",
  "Ocimum gratissimum", "Ocimum sanctum", "Oenanthe javanica", "Ophiopogon japonicus", "Paederia lanuginosa",
  "Pandanus amaryllifolius", "Pandanus sp", "Pandanus tectorius", "Parameria Laevigata", "Passiflora foetida",
  "Pereskia Sacharosa", "Persicaria odorata", "Phlogacanthus turgidus", "Phrynium placentarium", "Phyllanthus Reticulatus Poir",
  "Piper betle", "Piper sarmentosum", "Plantago", "Platycladus orientalis", "Plectranthus amboinicus",
  "Pluchea pteropoda Hemsl", "Plukenetia volubilis", "Plumbago indica", "Plumeria rubra", "Polyginum cuspidatum",
  "Polyscias fruticosa", "Polyscias guilfoylei", "Polyscias scutellaria", "Pouzolzia zeylanica", "Premna serratifolia",
  "Pseuderanthemum latifolium", "Psidium guajava", "Psychotria reevesii Wall", "Psychotria rubra", "Quisqualis indica",
  "Rauvolfia", "Rauvolfia tetraphylla", "Rhinacanthus nasutus", "Rhodomyrtus tomentosa", "Ruellia tuberosa",
  "Sanseviera canaliculata Carr", "Sansevieria hyacinthoides", "Sarcandra glabra", "Sauropus androgynus", "Schefflera heptaphylla",
  "Schefflera venulosa", "Senna alata", "Sida acuta Burm", "Solanum Mammosum", "Solanum torvum",
  "Spilanthes acmella", "Spondias dulcis", "Stachytarpheta jamaicensis", "Stephania dielsiana", "Stereospermum chelonoides",
  "Streptocaulon juventas", "Syzygium nervosum", "Tabernaemontana divaricata", "Tacca subflabellata", "Tamarindus indica",
  "Terminalia catappa", "Tradescantia discolor", "Trichanthera gigantea", "Vernonia amygdalina", "Vitex negundo",
  "Xanthium strumarium", "Zanthoxylum avicennae", "Zingiber officinale", "Ziziphus mauritiana", "helicteres hirsuta"
]; // 200 plant classes matching the model output

export const getPlantController = async (req, res) => {
  try {
    console.log(`Fetching plant: ${req.params.name}`);
    const { name } = req.params;
    
    const plant = await plantModel.findOne({ scientificName: decodeURIComponent(name) });
    console.log("Plant found in DB:", plant);

    if (!plant) {
      return res.status(404).json({
        success: false,
        message: "Plant not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Plant retrieved successfully",
      plant,
    });
  } catch (error) {
    console.error("Error retrieving plant:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting plant details",
    });
  }
};

export const uploadPlantController = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No file uploaded",
      });
    }

    const filePath = req.file.path;
    console.log("Processing file:", filePath);

    const response = await csv().fromFile(filePath);
    const plantData = response.map((entry) => ({
      scientificName: entry.scientificName?.trim(),
      localName: entry.localName?.trim(),
      features: entry.features?.trim(),
      photo: entry.photo?.trim(),
    }));

    if (plantData.some(p => !p.scientificName || !p.localName || !p.features)) {
      return res.status(400).json({
        success: false,
        message: "Invalid CSV data. Missing required fields.",
      });
    }

    await plantModel.insertMany(plantData);
    console.log("Plants added:", plantData.length);

    res.status(200).json({
      success: true,
      message: "Upload successful",
    });
  } catch (error) {
    console.error("Error uploading plant data:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in uploading plant details",
    });
  }
};

export const postFeedback = async (req, res) => {
  try {
    const { score, description } = req.body;
    if (!score || !description) {
      return res.status(400).json({
        success: false,
        message: "Score and description are required!",
      });
    }

    const feed = await new feedbackModel({ score, description }).save();
    res.status(200).json({
      success: true,
      message: "Review posted successfully",
      feed,
    });
  } catch (error) {
    console.error("Error posting feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in posting review",
    });
  }
};

export const getFeedback = async (req, res) => {
  try {
    const feed = await feedbackModel.find({}).sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      totalCount: feed.length,
      message: "Feedback retrieved",
      feed,
    });
  } catch (error) {
    console.error("Error fetching feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting feedback",
    });
  }
};

export const getResultName = async (req, res) => {
  try {
    // This endpoint now expects the plant name from frontend prediction
    const { plantName, predictionIndex } = req.body;

    if (!plantName && predictionIndex === undefined) {
      return res.status(400).json({
        success: false,
        message: "Plant name or prediction index required"
      });
    }

    let predictedPlant = plantName;

    // If prediction index is provided, get the plant name from class labels
    if (predictionIndex !== undefined && predictionIndex >= 0 && predictionIndex < classLabels.length) {
      predictedPlant = classLabels[predictionIndex];
    }

    console.log("Predicted Plant:", predictedPlant);

    res.status(200).json({
      success: true,
      message: "Plant identification completed",
      name: predictedPlant,
    });

  } catch (error) {
    console.error("Error processing plant identification:", error);
    res.status(500).json({
      success: false,
      error: error.message,
      message: "Error in processing plant identification"
    });
  }
};
