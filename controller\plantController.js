import * as tf from "@tensorflow/tfjs";
import fs from "fs";
import path from "path";
import plantModel from "../models/plantModel.js";
import feedbackModel from "../models/feedbackModel.js";

let model;

// Load the TensorFlow.js model
(async () => {
  try {
    model = await tf.loadLayersModel("file://public/model/model.json"); // ✅ Match folder structure
    console.log("✅ Model Loaded Successfully");
  } catch (error) {
    console.error("❌ Model Load Error:", error);
  }
})();

const classLabels = [
  "<PERSON><PERSON> Vera", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", 
  "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Coriander", "Fenugreek"
]; // Update based on your model training

export const getPlantController = async (req, res) => {
  try {
    console.log(`Fetching plant: ${req.params.name}`);
    const { name } = req.params;
    
    const plant = await plantModel.findOne({ scientificName: decodeURIComponent(name) });
    console.log("Plant found in DB:", plant);

    if (!plant) {
      return res.status(404).json({
        success: false,
        message: "Plant not found",
      });
    }

    res.status(200).json({
      success: true,
      message: "Plant retrieved successfully",
      plant,
    });
  } catch (error) {
    console.error("Error retrieving plant:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting plant details",
    });
  }
};

export const uploadPlantController = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: "No file uploaded",
      });
    }

    const filePath = req.file.path;
    console.log("Processing file:", filePath);

    const response = await csv().fromFile(filePath);
    const plantData = response.map((entry) => ({
      scientificName: entry.scientificName?.trim(),
      localName: entry.localName?.trim(),
      features: entry.features?.trim(),
      photo: entry.photo?.trim(),
    }));

    if (plantData.some(p => !p.scientificName || !p.localName || !p.features)) {
      return res.status(400).json({
        success: false,
        message: "Invalid CSV data. Missing required fields.",
      });
    }

    await plantModel.insertMany(plantData);
    console.log("Plants added:", plantData.length);

    res.status(200).json({
      success: true,
      message: "Upload successful",
    });
  } catch (error) {
    console.error("Error uploading plant data:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in uploading plant details",
    });
  }
};

export const postFeedback = async (req, res) => {
  try {
    const { score, description } = req.body;
    if (!score || !description) {
      return res.status(400).json({
        success: false,
        message: "Score and description are required!",
      });
    }

    const feed = await new feedbackModel({ score, description }).save();
    res.status(200).json({
      success: true,
      message: "Review posted successfully",
      feed,
    });
  } catch (error) {
    console.error("Error posting feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in posting review",
    });
  }
};

export const getFeedback = async (req, res) => {
  try {
    const feed = await feedbackModel.find({}).sort({ createdAt: -1 });
    res.status(200).json({
      success: true,
      totalCount: feed.length,
      message: "Feedback retrieved",
      feed,
    });
  } catch (error) {
    console.error("Error fetching feedback:", error);
    res.status(500).json({
      success: false,
      error,
      message: "Error in getting feedback",
    });
  }
};

export const getResultName = async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ success: false, message: "No file uploaded" });
    }

    const filePath = req.file.path;
    console.log("Processing file:", filePath);

    // Preprocess Image
    const imageBuffer = fs.readFileSync(filePath);
    let imageTensor = tf.node.decodeImage(imageBuffer)
      .resizeNearestNeighbor([224, 224]) // Resize image to match model input
      .toFloat()
      .div(tf.scalar(255.0)) // Normalize pixel values
      .expandDims(); // Add batch dimension

    // Run Model Inference
    const prediction = model.predict(imageTensor);
    const predictionArray = await prediction.array(); // Convert Tensor to JS array

    // Get Highest Probability Index
    const predictedIndex = predictionArray[0].indexOf(Math.max(...predictionArray[0]));
    const predictedPlant = classLabels[predictedIndex];

    console.log("Prediction Array:", predictionArray[0]);
    console.log("Predicted Index:", predictedIndex);
    console.log("Predicted Plant:", predictedPlant);

    res.status(200).json({
      success: true,
      message: "Model processing completed",
      name: predictedPlant,
    });

  } catch (error) {
    console.error("Error processing ML model:", error);
    res.status(500).json({ success: false, error, message: "Error in processing the model" });
  }
};
