{"format": "graph-model", "generatedBy": "2.13.0", "convertedBy": "TensorFlow.js Converter v4.11.0", "signature": {"inputs": {"sequential_input": {"name": "sequential_input:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "128"}, {"size": "128"}, {"size": "3"}]}}}, "outputs": {"outputs": {"name": "Identity:0", "dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "-1"}, {"size": "200"}]}}}}, "modelTopology": {"node": [{"name": "StatefulPartitionedCall/sequential_1/rescaling_1/Cast/x", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/rescaling_1/Cast_1/x", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "3"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "32"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "32"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "3"}, {"size": "3"}, {"size": "32"}, {"size": "64"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "64"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten/Const", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_INT32", "tensorShape": {"dim": [{"size": "2"}]}}}, "dtype": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense/MatMul/ReadVariableOp", "op": "Const", "attr": {"value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "16384"}, {"size": "128"}]}}}, "dtype": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/outputs/MatMul/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "128"}, {"size": "200"}]}}}}}, {"name": "StatefulPartitionedCall/sequential_1/outputs/BiasAdd/ReadVariableOp", "op": "Const", "attr": {"dtype": {"type": "DT_FLOAT"}, "value": {"tensor": {"dtype": "DT_FLOAT", "tensorShape": {"dim": [{"size": "200"}]}}}}}, {"name": "sequential_input", "op": "Placeholder", "attr": {"dtype": {"type": "DT_FLOAT"}, "shape": {"shape": {"dim": [{"size": "-1"}, {"size": "128"}, {"size": "128"}, {"size": "3"}]}}}}, {"name": "StatefulPartitionedCall/sequential_1/rescaling_1/mul", "op": "<PERSON><PERSON>", "input": ["sequential_input", "StatefulPartitionedCall/sequential_1/rescaling_1/Cast/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/rescaling_1/add", "op": "AddV2", "input": ["StatefulPartitionedCall/sequential_1/rescaling_1/mul", "StatefulPartitionedCall/sequential_1/rescaling_1/Cast_1/x"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/rescaling_1/add", "StatefulPartitionedCall/sequential_1/conv2d/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"filter_format": {"s": "SFdJTw=="}, "leakyrelu_alpha": {"f": 0.2}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "epsilon": {"f": 0.0}, "use_cudnn_on_gpu": {"b": true}, "num_host_args": {"i": "0"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "data_format": {"s": "TkhXQw=="}, "dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "padding": {"s": "U0FNRQ=="}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/conv2d/Relu"], "attr": {"explicit_paddings": {"list": {}}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}, "padding": {"s": "VkFMSUQ="}, "T": {"type": "DT_FLOAT"}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d/MaxPool", "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}, "data_format": {"s": "TkhXQw=="}, "explicit_paddings": {"list": {}}, "padding": {"s": "U0FNRQ=="}, "epsilon": {"f": 0.0}, "num_host_args": {"i": "0"}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "filter_format": {"s": "SFdJTw=="}, "num_args": {"i": "1"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "use_cudnn_on_gpu": {"b": true}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/conv2d_1/Relu"], "attr": {"T": {"type": "DT_FLOAT"}, "data_format": {"s": "TkhXQw=="}, "padding": {"s": "VkFMSUQ="}, "explicit_paddings": {"list": {}}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/Relu", "op": "_FusedConv2D", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_1/MaxPool", "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"dilations": {"list": {"i": ["1", "1", "1", "1"]}}, "num_host_args": {"i": "0"}, "epsilon": {"f": 0.0}, "filter_format": {"s": "SFdJTw=="}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "num_args": {"i": "1"}, "use_cudnn_on_gpu": {"b": true}, "padding": {"s": "U0FNRQ=="}, "data_format": {"s": "TkhXQw=="}, "T": {"type": "DT_FLOAT"}, "TArgs": {"list": {"type": ["DT_FLOAT"]}}, "explicit_paddings": {"list": {}}, "strides": {"list": {"i": ["1", "1", "1", "1"]}}, "leakyrelu_alpha": {"f": 0.2}}}, {"name": "StatefulPartitionedCall/sequential_1/max_pooling2d_2/MaxPool", "op": "MaxPool", "input": ["StatefulPartitionedCall/sequential_1/conv2d_2/Relu"], "attr": {"explicit_paddings": {"list": {}}, "padding": {"s": "VkFMSUQ="}, "data_format": {"s": "TkhXQw=="}, "ksize": {"list": {"i": ["1", "2", "2", "1"]}}, "T": {"type": "DT_FLOAT"}, "strides": {"list": {"i": ["1", "2", "2", "1"]}}}}, {"name": "StatefulPartitionedCall/sequential_1/flatten/Reshape", "op": "Reshape", "input": ["StatefulPartitionedCall/sequential_1/max_pooling2d_2/MaxPool", "StatefulPartitionedCall/sequential_1/flatten/Const"], "attr": {"T": {"type": "DT_FLOAT"}, "Tshape": {"type": "DT_INT32"}}}, {"name": "StatefulPartitionedCall/sequential_1/dense/Relu", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/flatten/Reshape", "StatefulPartitionedCall/sequential_1/dense/MatMul/ReadVariableOp", "StatefulPartitionedCall/sequential_1/dense/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"T": {"type": "DT_FLOAT"}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA==", "UmVsdQ=="]}}, "transpose_b": {"b": false}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}, "leakyrelu_alpha": {"f": 0.2}, "transpose_a": {"b": false}}}, {"name": "StatefulPartitionedCall/sequential_1/outputs/BiasAdd", "op": "_FusedMatMul", "input": ["StatefulPartitionedCall/sequential_1/dense/Relu", "StatefulPartitionedCall/sequential_1/outputs/MatMul/ReadVariableOp", "StatefulPartitionedCall/sequential_1/outputs/BiasAdd/ReadVariableOp"], "device": "/device:CPU:0", "attr": {"transpose_a": {"b": false}, "fused_ops": {"list": {"s": ["Qmlhc0FkZA=="]}}, "leakyrelu_alpha": {"f": 0.2}, "T": {"type": "DT_FLOAT"}, "transpose_b": {"b": false}, "epsilon": {"f": 0.0}, "num_args": {"i": "1"}}}, {"name": "StatefulPartitionedCall/sequential_1/outputs/Softmax", "op": "Softmax", "input": ["StatefulPartitionedCall/sequential_1/outputs/BiasAdd"], "attr": {"T": {"type": "DT_FLOAT"}}}, {"name": "Identity", "op": "Identity", "input": ["StatefulPartitionedCall/sequential_1/outputs/Softmax"], "attr": {"T": {"type": "DT_FLOAT"}}}], "library": {}, "versions": {"producer": 1482}}, "weightsManifest": [{"paths": ["group1-shard1of3.bin", "group1-shard2of3.bin", "group1-shard3of3.bin"], "weights": [{"name": "StatefulPartitionedCall/sequential_1/rescaling_1/Cast/x", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/rescaling_1/Cast_1/x", "shape": [], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d/Conv2D/ReadVariableOp", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/Conv2D/ReadVariableOp", "shape": [3, 3, 32, 32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_1/BiasAdd/ReadVariableOp", "shape": [32], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/Conv2D/ReadVariableOp", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/conv2d_2/BiasAdd/ReadVariableOp", "shape": [64], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/flatten/Const", "shape": [2], "dtype": "int32"}, {"name": "StatefulPartitionedCall/sequential_1/dense/MatMul/ReadVariableOp", "shape": [16384, 128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/dense/BiasAdd/ReadVariableOp", "shape": [128], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/outputs/MatMul/ReadVariableOp", "shape": [128, 200], "dtype": "float32"}, {"name": "StatefulPartitionedCall/sequential_1/outputs/BiasAdd/ReadVariableOp", "shape": [200], "dtype": "float32"}]}]}