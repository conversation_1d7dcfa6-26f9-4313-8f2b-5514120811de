import mongoose from "mongoose";

const connectDB = async () => {
  try {
    if (!process.env.MONGO_URI) {
      throw new Error("MONGO_URI is not defined in environment variables");
    }

    const conn = await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 5000, // Wait 5s before timing out
      autoIndex: true, // Automatically build indexes
    });

    console.log(
      `✅ Connected to MongoDB: ${conn.connection.host}`.bgMagenta.white
    );
  } catch (error) {
    console.error(`❌ MongoDB Connection Error: ${error.message}`.bgRed.white);
    process.exit(1); // Exit the process with failure
  }
};

// Handle unexpected errors to prevent crash
process.on("uncaughtException", (error) => {
  console.error(`🔥 Uncaught Exception: ${error.message}`.bgRed.white);
  process.exit(1);
});

export default connectDB;
